"use client";

import Link from "next/link";
import { usePathname } from "next/navigation";
import {
  Breadcrumb,
  BreadcrumbItem,
  BreadcrumbLink,
  BreadcrumbList,
  BreadcrumbPage,
  BreadcrumbSeparator,
} from "@/components/ui/breadcrumb";

interface BreadcrumbItem {
  label: string;
  href?: string;
}

interface BreadcrumbNavProps {
  items?: BreadcrumbItem[];
  className?: string;
}

// Default breadcrumb mapping for common routes
const routeLabels: Record<string, string> = {
  "": "Home",
  "good-moral": "Good Moral",
  affidavit: "Affidavit",
  clearance: "Clearance",
  license: "License",
  good_moral: "Good Moral",
  "pdf-scanner": "PDF Scanner",
};

function formatSegment(segment: string) {
  if (routeLabels[segment]) {
    return routeLabels[segment];
  }
  return segment
    .replace(/[-_]/g, " ")
    .split(" ")
    .map((word) => word.charAt(0).toUpperCase() + word.slice(1))
    .join(" ");
}

export function BreadcrumbNav({ items, className }: BreadcrumbNavProps) {
  const pathname = usePathname();

  // If custom items are provided, use them
  if (items) {
    return (
      <Breadcrumb className={className}>
        <BreadcrumbList>
          {items.map((item, index) => (
            <div key={index} className="flex items-center">
              <BreadcrumbItem>
                {item.href ? (
                  <BreadcrumbLink asChild>
                    <Link href={item.href}>{item.label}</Link>
                  </BreadcrumbLink>
                ) : (
                  <BreadcrumbPage>{item.label}</BreadcrumbPage>
                )}
              </BreadcrumbItem>
              {index < items.length - 1 && <BreadcrumbSeparator />}
            </div>
          ))}
        </BreadcrumbList>
      </Breadcrumb>
    );
  }

  // Auto-generate breadcrumbs from pathname
  const pathSegments = pathname.split("/").filter(Boolean);

  // Always start with Home
  const breadcrumbItems: BreadcrumbItem[] = [{ label: "Home", href: "/" }];

  // Build breadcrumb items from path segments
  const segmentsToSkip = ["pages", "templates", "form"];
  let currentPath = "";
  pathSegments.forEach((segment, index) => {
    currentPath += `/${segment}`;

    // Skip specific segments that shouldn't appear as breadcrumbs
    if (segmentsToSkip.includes(segment)) {
      return;
    }

    const label = formatSegment(segment);

    // Last item should not have href (current page)
    if (index === pathSegments.length - 1) {
      breadcrumbItems.push({ label });
    } else {
      breadcrumbItems.push({ label, href: currentPath });
    }
  });

  // Don't show breadcrumbs on home page
  if (pathname === "/") {
    return null;
  }

  return (
    <Breadcrumb className={className}>
      <BreadcrumbList>
        {breadcrumbItems.map((item, index) => (
          <div key={index} className="flex items-center">
            <BreadcrumbItem>
              {item.href ? (
                <BreadcrumbLink asChild>
                  <Link href={item.href}>{item.label}</Link>
                </BreadcrumbLink>
              ) : (
                <BreadcrumbPage>{item.label}</BreadcrumbPage>
              )}
            </BreadcrumbItem>
            {index < breadcrumbItems.length - 1 && <BreadcrumbSeparator />}
          </div>
        ))}
      </BreadcrumbList>
    </Breadcrumb>
  );
}
