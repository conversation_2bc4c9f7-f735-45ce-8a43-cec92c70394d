import { NextRequest, NextResponse } from 'next/server';
import { loadTemplates, loadTemplateFile, replacePlaceholders, generatePDFStyles } from '@/lib/templates';
import { replaceApplicantPhoto } from '@/lib/template-utils';
import fs from 'fs/promises';
import path from 'path';

export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    const { templateId, data, photoPath } = body;

    if (!templateId || !data) {
      return NextResponse.json(
        { error: 'Template ID and data are required' },
        { status: 400 }
      );
    }

    // Load templates to find the one to generate
    const templates = await loadTemplates();
    const template = templates.find(t => t.id === templateId);

    if (!template) {
      return NextResponse.json(
        { error: 'Template not found' },
        { status: 404 }
      );
    }

    // Load the template HTML content
    const templateContent = await loadTemplateFile(template.filename);

    // Replace placeholders with actual data
    let processedContent = replacePlaceholders(templateContent, data);

    // Replace applicant photo if provided
    if (photoPath) {
      processedContent = replaceApplicantPhoto(processedContent, photoPath);
    }

    // Create embedded data JSON for PDF scanning
    const embeddedData = {
      templateId: template.id,
      templateName: template.name,
      placeholders: template.placeholders,
      userData: data,
      photoBase64: photoPath ? await convertPhotoToBase64(photoPath) : undefined,
      generatedAt: new Date().toISOString(),
      layoutSize: template.layoutSize
    };

    // Add embedded data as hidden content in the HTML
    const embeddedDataJson = JSON.stringify(embeddedData);
    const embeddedDataHtml = `
      <!-- LDIS_EMBEDDED_DATA_START -->
      <script type="application/json" id="ldis-embedded-data" style="display: none;">
        ${embeddedDataJson}
      </script>
      <!-- LDIS_EMBEDDED_DATA_END -->
    `;

    // Add PDF-specific styles based on layout size
    const pdfStyles = generatePDFStyles(template.layoutSize);

    // Insert embedded data and styles before closing body tag
    let htmlWithStyles = processedContent.replace('</head>', `${pdfStyles}</head>`);
    htmlWithStyles = htmlWithStyles.replace('</body>', `${embeddedDataHtml}</body>`);

    // Return the HTML content with PDF styles for client-side PDF generation
    return NextResponse.json({
      success: true,
      htmlContent: htmlWithStyles,
      templateName: template.name,
      layoutSize: template.layoutSize
    });
  } catch (error) {
    console.error('Error generating document:', error);
    return NextResponse.json(
      { error: 'Failed to generate document' },
      { status: 500 }
    );
  }
}

// Helper function to convert photo to base64
async function convertPhotoToBase64(photoPath: string): Promise<string | undefined> {
  try {
    // Check if photoPath is already a base64 string
    if (photoPath.startsWith('data:')) {
      return photoPath;
    }

    // If it's a file path, read the file and convert to base64
    const fullPath = path.join(process.cwd(), 'public', photoPath);
    const fileBuffer = await fs.readFile(fullPath);
    const mimeType = photoPath.toLowerCase().endsWith('.png') ? 'image/png' : 'image/jpeg';
    const base64String = fileBuffer.toString('base64');
    return `data:${mimeType};base64,${base64String}`;
  } catch (error) {
    console.error('Error converting photo to base64:', error);
    return undefined;
  }
}
