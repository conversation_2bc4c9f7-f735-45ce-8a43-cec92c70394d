"use client";

import { useState } from "react";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import {
  Upload,
  FileText,
  AlertCircle,
  CheckCircle,
  Eye,
  Download,
} from "lucide-react";
import { toast } from "sonner";

interface PDFData {
  templateId: string;
  templateName: string;
  placeholders: string[];
  userData: Record<string, string>;
  photoBase64?: string;
  generatedAt: string;
  layoutSize: "A4" | "Letter";
}

interface ScanResult {
  success: boolean;
  data?: PDFData;
  rawText?: string;
  error?: string;
  debug?: {
    hasLdisMarkers: boolean;
    hasScriptTag: boolean;
    hasTemplateId: boolean;
    hasNewFormat: boolean;
    hasOldFormat: boolean;
    textLength: number;
  };
}

export default function PDFScannerPage() {
  const [selectedFile, setSelectedFile] = useState<File | null>(null);
  const [isScanning, setIsScanning] = useState(false);
  const [scanResult, setScanResult] = useState<ScanResult | null>(null);
  const [showRawText, setShowRawText] = useState(false);

  const handleFileSelect = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (file) {
      if (file.type !== "application/pdf") {
        toast.error("Please select a PDF file");
        return;
      }
      setSelectedFile(file);
      setScanResult(null);
    }
  };

  const handleScanPDF = async () => {
    if (!selectedFile) {
      toast.error("Please select a PDF file first");
      return;
    }

    setIsScanning(true);
    try {
      const formData = new FormData();
      formData.append("file", selectedFile);

      const response = await fetch("/api/pdf-scanner", {
        method: "POST",
        body: formData,
      });

      const result = await response.json();
      setScanResult(result);

      if (result.success) {
        toast.success("PDF scanned successfully!");
      } else {
        toast.error(result.error || "Failed to scan PDF");
      }
    } catch (error) {
      console.error("Error scanning PDF:", error);
      toast.error("Failed to scan PDF");
      setScanResult({ success: false, error: "Network error occurred" });
    } finally {
      setIsScanning(false);
    }
  };

  const formatDate = (dateString: string) => {
    try {
      return new Date(dateString).toLocaleString();
    } catch {
      return dateString;
    }
  };

  const downloadJSON = () => {
    if (!scanResult?.data) return;

    const dataStr = JSON.stringify(scanResult.data, null, 2);
    const dataBlob = new Blob([dataStr], { type: "application/json" });
    const url = URL.createObjectURL(dataBlob);
    const link = document.createElement("a");
    link.href = url;
    link.download = `${scanResult.data.templateName.replace(
      /[^a-z0-9]/gi,
      "_"
    )}_data.json`;
    link.click();
    URL.revokeObjectURL(url);
  };

  return (
    <div className="container mx-auto py-8 px-4">
      <div className="max-w-4xl mx-auto">
        <div className="mb-8">
          <h1 className="text-3xl font-bold mb-2">PDF Scanner</h1>
          <p className="text-muted-foreground">
            Upload a PDF generated by this system to extract the embedded user
            data and information.
          </p>
        </div>

        {/* File Upload Section */}
        <Card className="mb-6">
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Upload className="h-5 w-5" />
              Upload PDF
            </CardTitle>
            <CardDescription>
              Select a PDF file that was generated by the LDIS system to scan
              for embedded data.
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <div>
              <Label htmlFor="pdf-file">PDF File</Label>
              <Input
                id="pdf-file"
                type="file"
                accept=".pdf"
                onChange={handleFileSelect}
                className="mt-1"
              />
            </div>

            {selectedFile && (
              <div className="flex items-center gap-2 p-3 bg-muted rounded-lg">
                <FileText className="h-4 w-4" />
                <span className="text-sm">{selectedFile.name}</span>
                <span className="text-xs text-muted-foreground ml-auto">
                  {(selectedFile.size / 1024 / 1024).toFixed(2)} MB
                </span>
              </div>
            )}

            <Button
              onClick={handleScanPDF}
              disabled={!selectedFile || isScanning}
              className="w-full"
            >
              {isScanning ? "Scanning..." : "Scan PDF"}
            </Button>
          </CardContent>
        </Card>

        {/* Results Section */}
        {scanResult && (
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                {scanResult.success ? (
                  <CheckCircle className="h-5 w-5 text-green-500" />
                ) : (
                  <AlertCircle className="h-5 w-5 text-red-500" />
                )}
                Scan Results
              </CardTitle>
            </CardHeader>
            <CardContent>
              {scanResult.success && scanResult.data ? (
                <div className="space-y-6">
                  {/* Template Information */}
                  <div>
                    <h3 className="text-lg font-semibold mb-3">
                      Template Information
                    </h3>
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                      <div>
                        <Label className="text-sm font-medium">
                          Template ID
                        </Label>
                        <p className="text-sm text-muted-foreground font-mono">
                          {scanResult.data.templateId}
                        </p>
                      </div>
                      <div>
                        <Label className="text-sm font-medium">
                          Template Name
                        </Label>
                        <p className="text-sm text-muted-foreground">
                          {scanResult.data.templateName}
                        </p>
                      </div>
                      <div>
                        <Label className="text-sm font-medium">
                          Layout Size
                        </Label>
                        <p className="text-sm text-muted-foreground">
                          {scanResult.data.layoutSize}
                        </p>
                      </div>
                      <div>
                        <Label className="text-sm font-medium">
                          Generated At
                        </Label>
                        <p className="text-sm text-muted-foreground">
                          {formatDate(scanResult.data.generatedAt)}
                        </p>
                      </div>
                    </div>
                  </div>

                  {/* User Data */}
                  <div>
                    <h3 className="text-lg font-semibold mb-3">User Data</h3>
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                      {Object.entries(scanResult.data.userData).map(
                        ([key, value]) => (
                          <div key={key}>
                            <Label className="text-sm font-medium">{key}</Label>
                            <p className="text-sm text-muted-foreground">
                              {value || <span className="italic">Empty</span>}
                            </p>
                          </div>
                        )
                      )}
                    </div>
                  </div>

                  {/* Placeholders */}
                  <div>
                    <h3 className="text-lg font-semibold mb-3">
                      Placeholders ({scanResult.data.placeholders.length})
                    </h3>
                    <div className="flex flex-wrap gap-2">
                      {scanResult.data.placeholders.map(
                        (placeholder, index) => (
                          <span
                            key={index}
                            className="px-2 py-1 bg-muted text-sm rounded font-mono"
                          >
                            {placeholder}
                          </span>
                        )
                      )}
                    </div>
                  </div>

                  {/* Photo */}
                  {scanResult.data.photoBase64 && (
                    <div>
                      <h3 className="text-lg font-semibold mb-3">
                        Embedded Photo
                      </h3>
                      <div className="max-w-xs">
                        <img
                          src={scanResult.data.photoBase64}
                          alt="Embedded photo"
                          className="w-full h-auto border rounded-lg"
                        />
                      </div>
                    </div>
                  )}

                  {/* Debug Information */}
                  {scanResult.debug && (
                    <div>
                      <h3 className="text-lg font-semibold mb-3">
                        Debug Information
                      </h3>
                      <div className="grid grid-cols-2 gap-4 text-sm">
                        <div>
                          <Label className="font-medium">
                            Has LDIS Markers
                          </Label>
                          <p
                            className={
                              scanResult.debug.hasLdisMarkers
                                ? "text-green-600"
                                : "text-red-600"
                            }
                          >
                            {scanResult.debug.hasLdisMarkers ? "✓ Yes" : "✗ No"}
                          </p>
                        </div>
                        <div>
                          <Label className="font-medium">Has New Format</Label>
                          <p
                            className={
                              scanResult.debug.hasNewFormat
                                ? "text-green-600"
                                : "text-red-600"
                            }
                          >
                            {scanResult.debug.hasNewFormat ? "✓ Yes" : "✗ No"}
                          </p>
                        </div>
                        <div>
                          <Label className="font-medium">Has Old Format</Label>
                          <p
                            className={
                              scanResult.debug.hasOldFormat
                                ? "text-green-600"
                                : "text-red-600"
                            }
                          >
                            {scanResult.debug.hasOldFormat ? "✓ Yes" : "✗ No"}
                          </p>
                        </div>
                        <div>
                          <Label className="font-medium">Has Template ID</Label>
                          <p
                            className={
                              scanResult.debug.hasTemplateId
                                ? "text-green-600"
                                : "text-red-600"
                            }
                          >
                            {scanResult.debug.hasTemplateId ? "✓ Yes" : "✗ No"}
                          </p>
                        </div>
                        <div>
                          <Label className="font-medium">Text Length</Label>
                          <p className="text-muted-foreground">
                            {scanResult.debug.textLength.toLocaleString()}{" "}
                            characters
                          </p>
                        </div>
                      </div>
                    </div>
                  )}

                  {/* Actions */}
                  <div className="flex gap-2 pt-4 border-t">
                    <Button onClick={downloadJSON} variant="outline">
                      <Download className="h-4 w-4 mr-2" />
                      Download JSON
                    </Button>
                    <Button
                      onClick={() => setShowRawText(!showRawText)}
                      variant="outline"
                    >
                      <Eye className="h-4 w-4 mr-2" />
                      {showRawText ? "Hide" : "Show"} Raw Text
                    </Button>
                  </div>

                  {/* Raw Text */}
                  {showRawText && scanResult.rawText && (
                    <div>
                      <h3 className="text-lg font-semibold mb-3">
                        Raw PDF Text (Preview)
                      </h3>
                      <pre className="text-xs bg-muted p-4 rounded-lg overflow-auto max-h-64">
                        {scanResult.rawText}
                      </pre>
                    </div>
                  )}
                </div>
              ) : (
                <div className="space-y-6">
                  <div className="text-center py-8">
                    <AlertCircle className="h-12 w-12 text-red-500 mx-auto mb-4" />
                    <h3 className="text-lg font-semibold mb-2">Scan Failed</h3>
                    <p className="text-muted-foreground">
                      {scanResult.error ||
                        "Could not extract data from the PDF"}
                    </p>
                  </div>

                  {/* Debug Information for failed scans */}
                  {scanResult.debug && (
                    <div>
                      <h3 className="text-lg font-semibold mb-3">
                        Debug Information
                      </h3>
                      <div className="grid grid-cols-2 gap-4 text-sm">
                        <div>
                          <Label className="font-medium">
                            Has LDIS Markers
                          </Label>
                          <p
                            className={
                              scanResult.debug.hasLdisMarkers
                                ? "text-green-600"
                                : "text-red-600"
                            }
                          >
                            {scanResult.debug.hasLdisMarkers ? "✓ Yes" : "✗ No"}
                          </p>
                        </div>
                        <div>
                          <Label className="font-medium">Has New Format</Label>
                          <p
                            className={
                              scanResult.debug.hasNewFormat
                                ? "text-green-600"
                                : "text-red-600"
                            }
                          >
                            {scanResult.debug.hasNewFormat ? "✓ Yes" : "✗ No"}
                          </p>
                        </div>
                        <div>
                          <Label className="font-medium">Has Old Format</Label>
                          <p
                            className={
                              scanResult.debug.hasOldFormat
                                ? "text-green-600"
                                : "text-red-600"
                            }
                          >
                            {scanResult.debug.hasOldFormat ? "✓ Yes" : "✗ No"}
                          </p>
                        </div>
                        <div>
                          <Label className="font-medium">Has Template ID</Label>
                          <p
                            className={
                              scanResult.debug.hasTemplateId
                                ? "text-green-600"
                                : "text-red-600"
                            }
                          >
                            {scanResult.debug.hasTemplateId ? "✓ Yes" : "✗ No"}
                          </p>
                        </div>
                        <div>
                          <Label className="font-medium">Text Length</Label>
                          <p className="text-muted-foreground">
                            {scanResult.debug.textLength.toLocaleString()}{" "}
                            characters
                          </p>
                        </div>
                      </div>
                    </div>
                  )}

                  {/* Raw Text for failed scans */}
                  {scanResult.rawText && (
                    <div>
                      <h3 className="text-lg font-semibold mb-3">
                        Raw PDF Text (Preview)
                      </h3>
                      <pre className="text-xs bg-muted p-4 rounded-lg overflow-auto max-h-64">
                        {scanResult.rawText}
                      </pre>
                    </div>
                  )}
                </div>
              )}
            </CardContent>
          </Card>
        )}
      </div>
    </div>
  );
}
