import { NextRequest, NextResponse } from 'next/server';
import pdf from 'pdf-parse';

interface PDFData {
  templateId: string;
  templateName: string;
  placeholders: string[];
  userData: Record<string, string>;
  photoBase64?: string;
  generatedAt: string;
  layoutSize: 'A4' | 'Letter';
}

export async function POST(request: NextRequest) {
  try {
    const formData = await request.formData();
    const file = formData.get('file') as File;

    if (!file) {
      return NextResponse.json(
        { error: 'No file provided' },
        { status: 400 }
      );
    }

    if (file.type !== 'application/pdf') {
      return NextResponse.json(
        { error: 'File must be a PDF' },
        { status: 400 }
      );
    }

    // Convert file to buffer
    const buffer = Buffer.from(await file.arrayBuffer());

    // Parse PDF to extract text
    const pdfData = await pdf(buffer);
    const text = pdfData.text;

    // Try to extract structured data from the PDF text
    const extractedData = extractPDFData(text);

    if (!extractedData) {
      return NextResponse.json(
        { error: 'Could not extract valid data from PDF. The PDF might not contain embedded data or might not be generated by this system.' },
        { status: 400 }
      );
    }

    return NextResponse.json({
      success: true,
      data: extractedData,
      rawText: text.substring(0, 2000) + '...', // Include first 2000 chars for debugging
      debug: {
        hasLdisMarkers: text.includes('LDIS_EMBEDDED_DATA_START'),
        hasScriptTag: text.includes('ldis-embedded-data'),
        hasTemplateId: text.includes('"templateId"'),
        textLength: text.length
      }
    });

  } catch (error) {
    console.error('Error parsing PDF:', error);
    return NextResponse.json(
      { error: 'Failed to parse PDF' },
      { status: 500 }
    );
  }
}

function extractPDFData(text: string): PDFData | null {
  try {
    // Look for embedded data between our new markers
    const embeddedDataMatch = text.match(/LDIS_EMBEDDED_DATA_START([\s\S]*?)LDIS_EMBEDDED_DATA_END/);

    if (embeddedDataMatch) {
      const embeddedText = embeddedDataMatch[1];

      // Try to find JSON structure in the embedded text
      const jsonMatch = embeddedText.match(/\{[\s\S]*?\}/);
      if (jsonMatch) {
        try {
          const jsonData = JSON.parse(jsonMatch[0]);

          // Validate that it has the expected structure
          if (jsonData.templateId && jsonData.templateName && jsonData.userData) {
            return jsonData as PDFData;
          }
        } catch (error) {
          console.error('Error parsing embedded JSON:', error);
        }
      }
    }

    // Look for the script tag pattern
    const scriptMatch = text.match(/ldis-embedded-data[^{]*(\{[\s\S]*?\})/);
    if (scriptMatch) {
      try {
        const jsonData = JSON.parse(scriptMatch[1]);
        if (jsonData.templateId && jsonData.templateName && jsonData.userData) {
          return jsonData as PDFData;
        }
      } catch (error) {
        console.error('Error parsing script JSON:', error);
      }
    }

    // Fallback: Try to find JSON with templateId anywhere in the text
    const templateIdIndex = text.indexOf('"templateId"');
    if (templateIdIndex !== -1) {
      // Find the start of the JSON object
      let startIndex = templateIdIndex;
      while (startIndex > 0 && text[startIndex] !== '{') {
        startIndex--;
      }

      if (text[startIndex] === '{') {
        // Find the matching closing brace
        let braceCount = 0;
        let endIndex = startIndex;

        for (let i = startIndex; i < text.length; i++) {
          if (text[i] === '{') braceCount++;
          if (text[i] === '}') braceCount--;
          if (braceCount === 0) {
            endIndex = i;
            break;
          }
        }

        if (endIndex > startIndex) {
          try {
            const jsonString = text.substring(startIndex, endIndex + 1);
            const jsonData = JSON.parse(jsonString);
            if (jsonData.templateId && jsonData.templateName && jsonData.userData) {
              return jsonData as PDFData;
            }
          } catch (error) {
            console.error('Error parsing extracted JSON:', error);
          }
        }
      }
    }

    // If no structured data found, try to extract from text patterns
    return extractFromTextPatterns(text);

  } catch (error) {
    console.error('Error extracting PDF data:', error);
    return null;
  }
}

function extractFromTextPatterns(text: string): PDFData | null {
  try {
    const extractedData: PDFData = {
      templateId: extractValue(text, /Template ID:\s*([^\n\r]+)/) || 'unknown',
      templateName: extractValue(text, /Template Name:\s*([^\n\r]+)/) || 'Unknown Template',
      placeholders: extractPlaceholders(text),
      userData: extractUserData(text),
      generatedAt: extractValue(text, /Generated At:\s*([^\n\r]+)/) || new Date().toISOString(),
      layoutSize: (extractValue(text, /Layout Size:\s*([^\n\r]+)/) as 'A4' | 'Letter') || 'A4'
    };

    // Check if we found meaningful data
    if (Object.keys(extractedData.userData).length === 0) {
      return null;
    }

    return extractedData;

  } catch (error) {
    console.error('Error extracting from text patterns:', error);
    return null;
  }
}

function extractValue(text: string, pattern: RegExp): string | null {
  const match = text.match(pattern);
  return match ? match[1].trim() : null;
}

function extractPlaceholders(text: string): string[] {
  // Extract placeholders in [PLACEHOLDER] format
  const placeholderMatches = text.match(/\[([^\]]+)\]/g);
  return placeholderMatches ? placeholderMatches : [];
}

function extractUserData(text: string): Record<string, string> {
  const userData: Record<string, string> = {};
  
  // Common patterns for extracting user data
  const patterns = [
    { key: 'FIRST NAME', pattern: /(?:First Name|FIRST NAME):\s*([^\n\r]+)/i },
    { key: 'LAST NAME', pattern: /(?:Last Name|LAST NAME):\s*([^\n\r]+)/i },
    { key: 'MIDDLE INITIAL', pattern: /(?:Middle Initial|MIDDLE INITIAL):\s*([^\n\r]+)/i },
    { key: 'AGE', pattern: /(?:Age|AGE):\s*([^\n\r]+)/i },
    { key: 'BARANGAY', pattern: /(?:Barangay|BARANGAY):\s*([^\n\r]+)/i },
    { key: 'CTC NUMBER', pattern: /(?:CTC Number|CTC NUMBER):\s*([^\n\r]+)/i },
    { key: 'O.R. NUMBER', pattern: /(?:O\.R\. Number|O\.R\. NUMBER):\s*([^\n\r]+)/i },
    { key: 'TIN NUMBER', pattern: /(?:TIN Number|TIN NUMBER):\s*([^\n\r]+)/i },
    { key: 'SUFFIX', pattern: /(?:Suffix|SUFFIX):\s*([^\n\r]+)/i },
    { key: 'DAY', pattern: /(?:Day|DAY):\s*([^\n\r]+)/i },
    { key: 'MONTH', pattern: /(?:Month|MONTH):\s*([^\n\r]+)/i },
    { key: 'YEAR', pattern: /(?:Year|YEAR):\s*([^\n\r]+)/i },
  ];

  patterns.forEach(({ key, pattern }) => {
    const value = extractValue(text, pattern);
    if (value) {
      userData[key] = value;
    }
  });

  return userData;
}
